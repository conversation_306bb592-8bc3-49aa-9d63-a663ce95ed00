//
//  CircularSpectrumView.swift
//  SoundVisualizer
//
//  Created by Augment Agent on 2025-06-24.
//

import SwiftUI

/**
 * Stunning circular spectrum visualization with smooth 60fps rendering.
 * 
 * Features:
 * - Real-time circular frequency spectrum display
 * - Rotating and pulsing animations based on audio intensity
 * - Color-reactive visualization with beautiful gradients
 * - Optimized rendering using SwiftUI Canvas for 60fps performance
 * - Multiple concentric rings for depth and visual appeal
 */
struct CircularSpectrumView: View {
    @ObservedObject var audioEngine: AudioEngine
    
    // Animation and visual state
    @State private var rotationAngle: Double = 0
    @State private var pulseScale: CGFloat = 1.0
    @State private var colorPhase: Double = 0
    @State private var spectrumData: [Float] = []
    
    // Configuration
    private let numberOfBars = 128
    private let innerRadius: CGFloat = 40
    private let maxBarLength: CGFloat = 80
    private let smoothingFactor: Float = 0.8
    
    var body: some View {
        GeometryReader { geometry in
            Canvas { context, size in
                drawCircularSpectrum(context: context, size: size)
            }
            .onReceive(Timer.publish(every: 1/60.0, on: .main, in: .common).autoconnect()) { _ in
                updateAnimations()
            }
        }
        .background(Color.clear)
        .clipped()
        .onAppear {
            initializeSpectrum()
        }
    }
    
    // MARK: - Initialize Spectrum
    private func initializeSpectrum() {
        spectrumData = Array(repeating: 0.0, count: numberOfBars)
    }
    
    // MARK: - Update Animations
    private func updateAnimations() {
        let frequencyData = audioEngine.frequencyData
        guard !frequencyData.isEmpty else { return }
        
        // Update spectrum data with smoothing
        let dataPerBar = max(1, frequencyData.count / numberOfBars)
        var newSpectrumData: [Float] = []
        
        for i in 0..<numberOfBars {
            let startIndex = i * dataPerBar
            let endIndex = min(startIndex + dataPerBar, frequencyData.count)
            
            var sum: Float = 0
            for j in startIndex..<endIndex {
                sum += frequencyData[j]
            }
            let average = sum / Float(endIndex - startIndex)
            
            // Apply logarithmic scaling and smoothing
            let logValue = log10(max(average * 5 + 1, 1.1)) / log10(6)
            let currentValue = i < spectrumData.count ? spectrumData[i] : 0
            let smoothedValue = currentValue * smoothingFactor + logValue * (1 - smoothingFactor)
            
            newSpectrumData.append(smoothedValue)
        }
        
        // Update with animation
        withAnimation(.linear(duration: 0.016)) {
            spectrumData = newSpectrumData
            
            // Update rotation based on audio intensity
            let rotationSpeed = Double(audioEngine.averageLevel * 30 + 5)
            rotationAngle += rotationSpeed * 0.016
            
            // Update pulse scale
            let targetScale = 1.0 + CGFloat(audioEngine.peakLevel * 0.3)
            pulseScale = pulseScale * 0.9 + targetScale * 0.1
            
            // Update color phase
            colorPhase += Double(audioEngine.averageLevel * 2 + 0.5)
        }
        
        // Reset angles to prevent overflow
        if rotationAngle > 360 {
            rotationAngle -= 360
        }
        if colorPhase > 360 {
            colorPhase -= 360
        }
    }
    
    // MARK: - Draw Circular Spectrum
    private func drawCircularSpectrum(context: GraphicsContext, size: CGSize) {
        let center = CGPoint(x: size.width / 2, y: size.height / 2)
        let radius = min(size.width, size.height) / 2 - 20
        let adjustedInnerRadius = min(innerRadius, radius * 0.3)
        let adjustedMaxBarLength = min(maxBarLength, radius - adjustedInnerRadius)
        
        // Draw background circles
        drawBackgroundCircles(context: context, center: center, radius: radius)
        
        // Draw spectrum bars
        drawSpectrumBars(
            context: context,
            center: center,
            innerRadius: adjustedInnerRadius,
            maxBarLength: adjustedMaxBarLength
        )
        
        // Draw center circle
        drawCenterCircle(context: context, center: center, radius: adjustedInnerRadius)
        
        // Draw outer glow
        if audioEngine.averageLevel > 0.2 {
            drawOuterGlow(context: context, center: center, radius: radius)
        }
    }
    
    // MARK: - Draw Background Circles
    private func drawBackgroundCircles(context: GraphicsContext, center: CGPoint, radius: CGFloat) {
        let circleCount = 5
        
        for i in 0..<circleCount {
            let circleRadius = radius * CGFloat(i + 1) / CGFloat(circleCount + 1)
            let opacity = 0.1 - Double(i) * 0.015
            
            let circlePath = Path { path in
                path.addEllipse(in: CGRect(
                    x: center.x - circleRadius,
                    y: center.y - circleRadius,
                    width: circleRadius * 2,
                    height: circleRadius * 2
                ))
            }
            
            context.stroke(
                circlePath,
                with: .color(Color.white.opacity(opacity)),
                style: StrokeStyle(lineWidth: 0.5, dash: [2, 4])
            )
        }
    }
    
    // MARK: - Draw Spectrum Bars
    private func drawSpectrumBars(context: GraphicsContext, center: CGPoint, innerRadius: CGFloat, maxBarLength: CGFloat) {
        let angleStep = 2 * Double.pi / Double(numberOfBars)
        
        for (index, value) in spectrumData.enumerated() {
            let angle = Double(index) * angleStep + rotationAngle * Double.pi / 180
            let barLength = CGFloat(value) * maxBarLength * pulseScale
            
            // Calculate bar positions
            let startRadius = innerRadius
            let endRadius = startRadius + barLength
            
            let startX = center.x + cos(angle) * startRadius
            let startY = center.y + sin(angle) * startRadius
            let endX = center.x + cos(angle) * endRadius
            let endY = center.y + sin(angle) * endRadius
            
            // Draw bar with gradient
            drawSpectrumBar(
                context: context,
                start: CGPoint(x: startX, y: startY),
                end: CGPoint(x: endX, y: endY),
                intensity: CGFloat(value),
                index: index
            )
        }
    }
    
    // MARK: - Draw Individual Spectrum Bar
    private func drawSpectrumBar(context: GraphicsContext, start: CGPoint, end: CGPoint, intensity: CGFloat, index: Int) {
        let path = Path { path in
            path.move(to: start)
            path.addLine(to: end)
        }
        
        // Calculate color based on position and intensity
        let normalizedIndex = Double(index) / Double(numberOfBars)
        let hue = (normalizedIndex + colorPhase / 360).truncatingRemainder(dividingBy: 1.0)
        let saturation = 0.8 + Double(intensity) * 0.2
        let brightness = 0.5 + Double(intensity) * 0.5
        
        let barColor = Color(hue: hue, saturation: saturation, brightness: brightness)
        
        // Calculate line width based on intensity
        let lineWidth = 2.0 + intensity * 3.0
        
        // Draw main bar
        context.stroke(
            path,
            with: .color(barColor),
            style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
        )
        
        // Add glow effect for high-intensity bars
        if intensity > 0.6 {
            context.stroke(
                path,
                with: .color(barColor.opacity(0.5)),
                style: StrokeStyle(lineWidth: lineWidth * 2, lineCap: .round)
            )
            context.addFilter(.blur(radius: intensity * 3))
        }
        
        // Add sparkle effect for very high intensity
        if intensity > 0.8 {
            let sparkleRadius = intensity * 4
            let sparklePath = Path { path in
                path.addEllipse(in: CGRect(
                    x: end.x - sparkleRadius,
                    y: end.y - sparkleRadius,
                    width: sparkleRadius * 2,
                    height: sparkleRadius * 2
                ))
            }
            
            context.fill(sparklePath, with: .color(Color.white.opacity(0.8)))
            context.addFilter(.blur(radius: 2))
        }
    }
    
    // MARK: - Draw Center Circle
    private func drawCenterCircle(context: GraphicsContext, center: CGPoint, radius: CGFloat) {
        let centerPath = Path { path in
            path.addEllipse(in: CGRect(
                x: center.x - radius,
                y: center.y - radius,
                width: radius * 2,
                height: radius * 2
            ))
        }
        
        // Create radial gradient for center
        let centerGradient = RadialGradient(
            colors: [
                Color.cyan.opacity(0.8),
                Color.purple.opacity(0.6),
                Color.black.opacity(0.9)
            ],
            center: .center,
            startRadius: 0,
            endRadius: radius
        )
        
        context.fill(
            centerPath,
            with: .radialGradient(
                centerGradient,
                center: center,
                startRadius: 0,
                endRadius: radius
            )
        )
        
        // Add border
        context.stroke(
            centerPath,
            with: .color(Color.white.opacity(0.3)),
            style: StrokeStyle(lineWidth: 2)
        )
        
        // Add pulsing inner circle
        let innerRadius = radius * 0.6 * pulseScale
        let innerPath = Path { path in
            path.addEllipse(in: CGRect(
                x: center.x - innerRadius,
                y: center.y - innerRadius,
                width: innerRadius * 2,
                height: innerRadius * 2
            ))
        }
        
        context.fill(
            innerPath,
            with: .color(Color.cyan.opacity(Double(audioEngine.averageLevel * 0.5)))
        )
    }
    
    // MARK: - Draw Outer Glow
    private func drawOuterGlow(context: GraphicsContext, center: CGPoint, radius: CGFloat) {
        let glowRadius = radius + CGFloat(audioEngine.averageLevel * 20)
        let glowPath = Path { path in
            path.addEllipse(in: CGRect(
                x: center.x - glowRadius,
                y: center.y - glowRadius,
                width: glowRadius * 2,
                height: glowRadius * 2
            ))
        }
        
        let glowColor = Color.cyan.opacity(Double(audioEngine.averageLevel * 0.3))
        context.stroke(
            glowPath,
            with: .color(glowColor),
            style: StrokeStyle(lineWidth: 3)
        )
        context.addFilter(.blur(radius: 10))
    }
}

// MARK: - Preview
#Preview {
    CircularSpectrumView(audioEngine: AudioEngine())
        .frame(width: 400, height: 400)
        .background(Color.black)
}
